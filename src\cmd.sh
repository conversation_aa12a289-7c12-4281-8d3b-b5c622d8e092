````bash path=start_system.sh mode=EDIT
#!/bin/bash

echo "=== 机器人系统启动脚本 ==="
echo "1. 启动雷达"
echo "2. 启动里程计"
echo "3. 计算yaw"
echo "4. 启动串口"
echo "5. 计算发射角度"
echo "6. 获得当前pitch"
echo "7. 查看数据"
echo "8. 退出"
echo

read -p "请选择操作 (1-8): " choice

case $choice in
    1)
        echo "启动雷达..."
        roslaunch livox_ros_driver2 msg_MID360.launch
        ;;
    2)
        echo "启动里程计..."
        roslaunch faster_lio mapping_avia.launch relocalization_enable:=0 pcd_save_en:=0
        ;;
    3)
        echo "计算yaw..."
        rosrun faster_lio basket_calculator.py
        ;;
    4)
        echo "启动串口..."
        rosrun my_pkg1 test6.py
        ;;
    5)
        echo "计算发射角度..."
        rosrun my_pkg1 test8.py
        ;;
    6)
        echo "获得当前pitch..."
        rosrun my_pkg1 cam_test1.py
        ;;
    7)
        echo "查看数据..."
        rosrun faster_lio basket_robot_position.py
        ;;
    8)
        echo "退出"
        exit 0
        ;;
    *)
        echo "无效选择"
        exit 1
        ;;
esac
````
