#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import rospy

import math
from sensor_msgs.msg import Imu
from geometry_msgs.msg import Vector3
from tf.transformations import euler_from_quaternion

# 定义一个全局变量来存储发布者
angle_publisher = None

def imu_callback(imu_msg):
    """
    这是一个回调函数。每当/wit/imu话题有新消息时，ROS就会调用这个函数。
    """
    global angle_publisher

    # 1. 从IMU消息中提取姿态四元数
    # orientation是一个包含x, y, z, w四个分量的对象
    orientation_q = imu_msg.orientation
    orientation_list = [orientation_q.x, orientation_q.y, orientation_q.z, orientation_q.w]

    # 2. 将四元数转换为欧拉角（横滚角、俯仰角、航向角）
    # euler_from_quaternion的返回值是弧度(radians)
    (roll, pitch, yaw) = euler_from_quaternion(orientation_list)

    # --- 为了方便人类阅读，我们将弧度转换为角度 ---
    # 横滚角 (Roll)
    roll_deg = math.degrees(roll)
    # 俯仰角 (Pitch)
    pitch_deg = math.degrees(pitch)
    # 航向角 (Yaw)
    yaw_deg = math.degrees(yaw)

    # 打印到终端进行调试（可选）
    # rospy.loginfo("角度[度]: 横滚=%.2f, 俯仰=%.2f, 航向=%.2f", roll_deg, pitch_deg, yaw_deg)

    # 3. 创建一个新的消息并发布
    # 我们使用一个Vector3类型的消息来发布这三个角度值
    # x: 横滚角 (Roll)
    # y: 俯仰角 (Pitch)
    # z: 航向角 (Yaw)
    angle_msg = Vector3()
    angle_msg.x = roll_deg
    angle_msg.y= pitch_deg
    angle_msg.z = yaw_deg
    
    # 确保发布者已创建
    if angle_publisher is not None:
        angle_publisher.publish(angle_msg)

def main():
    """
    主函数，用于设置ROS节点、订阅者和发布者
    """
    global angle_publisher

    # 初始化ROS节点，节点名为'angle_converter'
    rospy.init_node('angle_converter', anonymous=True)

    # 创建一个发布者，它会向/current_angle话题发布消息
    # 消息类型是Vector3，队列大小是10
    angle_publisher = rospy.Publisher('/Ncurrent_angle', Vector3, queue_size=10)

    # 创建一个订阅者，它会订阅/wit/imu话题
    # 每收到一条Imu消息，就调用imu_callback函数进行处理
    rospy.Subscriber('/wit/imu', Imu, imu_callback)

    rospy.loginfo("角度转换节点已启动. 正在监听 /wit/imu 并发布到 /current_angle...")

    # rospy.spin()让程序保持运行，持续监听话题
    rospy.spin()

if __name__ == '__main__':
    try:
        main()
    except rospy.ROSInterruptException:
        pass